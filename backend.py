from flask import Flask, request, jsonify, render_template, session, redirect, url_for
from flask_session import Session
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
from werkzeug.security import generate_password_hash, check_password_hash
import subprocess
import os
import datetime
import json
import tempfile
from time import sleep
import sys
import re
import secrets
import threading

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

# Configure session
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_PERMANENT'] = False
app.config['SESSION_USE_SIGNER'] = True
app.config['SESSION_FILE_DIR'] = './sessions'
app.config['PERMANENT_SESSION_LIFETIME'] = datetime.timedelta(hours=24)

# Initialize session
Session(app)

# Create sessions directory if it doesn't exist
os.makedirs('./sessions', exist_ok=True)

# Simple user database (in production, use a proper database)
USERS = {
    '<EMAIL>': {
        'password_hash': generate_password_hash('admin123'),
        'name': 'Administrator',
        'role': 'admin'
    },
    '<EMAIL>': {
        'password_hash': generate_password_hash('user123'),
        'name': 'User',
        'role': 'user'
    }
}

# User activity tracking
USER_ACTIVITIES = {}
ACTIVE_SESSIONS = {}

def log_user_activity(user_id, activity, details=None):
    """Log user activity for tracking"""
    if user_id not in USER_ACTIVITIES:
        USER_ACTIVITIES[user_id] = []

    activity_entry = {
        'timestamp': datetime.datetime.now().isoformat(),
        'activity': activity,
        'details': details or {}
    }

    USER_ACTIVITIES[user_id].append(activity_entry)

    # Keep only last 100 activities per user
    if len(USER_ACTIVITIES[user_id]) > 100:
        USER_ACTIVITIES[user_id] = USER_ACTIVITIES[user_id][-100:]

def update_session_activity(user_id):
    """Update last activity time for session"""
    if user_id in ACTIVE_SESSIONS:
        ACTIVE_SESSIONS[user_id]['last_activity'] = datetime.datetime.now().isoformat()
    else:
        ACTIVE_SESSIONS[user_id] = {
            'login_time': datetime.datetime.now().isoformat(),
            'last_activity': datetime.datetime.now().isoformat()
        }


tool_path = ''
temp_json_path = ''
output_path = ''
mode = ''

# Authentication helper functions
def validate_email(email):
    """Validate email format and domain restriction"""
    pattern = r'^[a-zA-Z0-9._%+-]+@company\.com$'
    return re.match(pattern, email) is not None

def is_authenticated():
    """Check if user is authenticated"""
    return 'user_id' in session and session.get('authenticated', False)

def require_auth(f):
    """Decorator to require authentication"""
    def decorated_function(*args, **kwargs):
        if not is_authenticated():
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Authentication routes
@app.route('/login')
def login_page():
    """Serve login page"""
    if is_authenticated():
        return redirect(url_for('index'))
    return render_template('login.html')

@app.route('/api/login', methods=['POST'])
def login():
    """Handle login requests"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        remember_me = data.get('remember_me', False)

        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format. Please use @company.com domain.'
            }), 400

        # Check if user exists and password is correct
        if email in USERS and check_password_hash(USERS[email]['password_hash'], password):
            # Create session
            session['user_id'] = email
            session['user_name'] = USERS[email]['name']
            session['user_role'] = USERS[email]['role']
            session['authenticated'] = True
            session['login_time'] = datetime.datetime.now().isoformat()

            if remember_me:
                session.permanent = True

            # Log user activity and update session tracking
            log_user_activity(email, 'login', {
                'ip_address': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'remember_me': remember_me
            })
            update_session_activity(email)

            # Broadcast login notification
            broadcast_notification(email, f'Welcome back, {USERS[email]["name"]}! 🚀', 'success')

            return jsonify({
                'success': True,
                'message': 'Login successful',
                'user': {
                    'email': email,
                    'name': USERS[email]['name'],
                    'role': USERS[email]['role']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401

    except Exception as e:
        return jsonify({
            'success': False,
            'message': 'Login failed due to server error'
        }), 500

@app.route('/api/logout', methods=['POST'])
def logout():
    """Handle logout requests"""
    user_id = session.get('user_id')

    if user_id:
        # Log logout activity
        log_user_activity(user_id, 'logout', {
            'ip_address': request.remote_addr,
            'session_duration': calculate_session_duration(user_id)
        })

        # Remove from active sessions
        if user_id in ACTIVE_SESSIONS:
            del ACTIVE_SESSIONS[user_id]

    session.clear()
    return jsonify({'success': True, 'message': 'Logged out successfully'})

def calculate_session_duration(user_id):
    """Calculate session duration in minutes"""
    if user_id in ACTIVE_SESSIONS:
        login_time = datetime.datetime.fromisoformat(ACTIVE_SESSIONS[user_id]['login_time'])
        duration = datetime.datetime.now() - login_time
        return round(duration.total_seconds() / 60, 2)
    return 0

@app.route('/api/session', methods=['GET'])
def get_session_info():
    """Get current session information"""
    if is_authenticated():
        user_id = session['user_id']
        update_session_activity(user_id)

        return jsonify({
            'authenticated': True,
            'user': {
                'email': session['user_id'],
                'name': session['user_name'],
                'role': session['user_role'],
                'login_time': session['login_time']
            }
        })
    else:
        return jsonify({'authenticated': False})

@app.route('/api/sessions', methods=['GET'])
@require_auth
def get_all_sessions():
    """Get all active sessions (admin only)"""
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Admin access required'}), 403

    return jsonify({
        'active_sessions': ACTIVE_SESSIONS,
        'total_count': len(ACTIVE_SESSIONS)
    })

@app.route('/api/user-activity', methods=['GET'])
@require_auth
def get_user_activity():
    """Get current user's activity log"""
    user_id = session['user_id']
    activities = USER_ACTIVITIES.get(user_id, [])

    # Return last 20 activities
    return jsonify({
        'activities': activities[-20:],
        'total_count': len(activities)
    })

@app.route('/api/user-activity/all', methods=['GET'])
@require_auth
def get_all_user_activities():
    """Get all user activities (admin only)"""
    if session.get('user_role') != 'admin':
        return jsonify({'error': 'Admin access required'}), 403

    return jsonify({
        'user_activities': USER_ACTIVITIES,
        'active_sessions': ACTIVE_SESSIONS
    })

@app.route('/')
def index():
    """Serve main application page"""
    if not is_authenticated():
        return redirect(url_for('login_page'))
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
@require_auth
def upload():
    global output_path
    global tool_path
    global temp_json_path
    global mode

    user_id = session['user_id']
    current_time = datetime.datetime.now().strftime("%d-%m-%Y_%H%M%S")
    input_dict = {}

    uploaded_file = request.files['file']
    tool_name = request.form['tool']
    mode = request.form['mode']

    # Log file upload activity
    log_user_activity(user_id, 'file_upload', {
        'filename': uploaded_file.filename,
        'tool_name': tool_name,
        'mode': mode,
        'file_size': len(uploaded_file.read())
    })

    # Reset file pointer after reading for logging
    uploaded_file.seek(0)

    output_path = f"AutoSpace_output_files\{tool_name}_{current_time}_output.txt"
    tool_path = f"python_tools/{tool_name}.py"

    uploaded_file_content = uploaded_file.read().decode('utf-8', errors='ignore')

    for id, row in enumerate(uploaded_file_content.split("\n"), 1):
        input_dict[id] = row

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix=".json") as temp_json:
        json.dump(input_dict, temp_json)
        temp_json_path = temp_json.name

    update_session_activity(user_id)

    # Broadcast file upload notification
    broadcast_notification(user_id, f'File "{uploaded_file.filename}" uploaded successfully', 'success')
    broadcast_session_update(user_id, 'file_upload', {
        'filename': uploaded_file.filename,
        'tool_name': tool_name,
        'estimated_time': estimate_processing_time(len(input_dict), tool_name)
    })

    return jsonify({
        'message': 'success',
        'session_id': f"{tool_name}_{current_time}",
        'estimated_processing_time': estimate_processing_time(len(input_dict), tool_name)
    })

def estimate_processing_time(line_count, tool_name):
    """Estimate processing time based on file size and tool"""
    base_time = {
        'extractor': 0.1,
        'keyword_search': 0.2,
        'equal_manual': 0.5,
        'acrobat_sim': 0.3
    }

    multiplier = base_time.get(tool_name, 0.2)
    estimated_seconds = line_count * multiplier

    if estimated_seconds < 60:
        return f"{int(estimated_seconds)} seconds"
    else:
        return f"{int(estimated_seconds / 60)} minutes"


@app.route('/runtool', methods=['POST'])
@require_auth
def runtool():
    global output_path
    global tool_path
    global temp_json_path
    global mode

    user_id = session['user_id']
    start_time = datetime.datetime.now()

    # Log tool execution start
    log_user_activity(user_id, 'tool_execution_start', {
        'tool_path': tool_path,
        'mode': mode,
        'start_time': start_time.isoformat()
    })

    # Broadcast tool execution start
    broadcast_notification(user_id, 'Tool execution started', 'info')
    broadcast_session_update(user_id, 'tool_execution_start', {
        'tool_path': tool_path,
        'mode': mode,
        'start_time': start_time.isoformat()
    })

    current_path = os.getcwd()
    output_path = os.path.join(current_path, output_path)
    total_output = 0

    status = 'running'

    try:
        while True:
            if os.stat(temp_json_path).st_size == 2:
                break

            if status == 'finished':
                break

            process = subprocess.Popen(
            ["python", "-u", tool_path, temp_json_path, output_path, mode])

            with open(temp_json_path, 'r') as f:
                input_dict = json.load(f)

            sleep(10)

            while True:
                old_file_size = os.stat(output_path).st_size

                sleep(60)
                if os.stat(temp_json_path).st_size == 2:
                    status = 'finished'
                    break

                if os.stat(output_path).st_size == old_file_size:
                        process.terminate()

                        with open(output_path, 'r', encoding='utf8') as f:
                            lines = f.readlines()

                        loop_finished_rows = len(lines) - total_output
                        residual_ids = dict(list(input_dict.items())[loop_finished_rows+1:])

                        with open(temp_json_path, 'w') as f:
                            json.dump(residual_ids, f)

                        total_output = len(lines)
                        print("input_file updated and process restarted")
                        break

        # Log successful completion
        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()

        log_user_activity(user_id, 'tool_execution_complete', {
            'output_path': output_path,
            'duration_seconds': duration,
            'total_lines_processed': total_output,
            'end_time': end_time.isoformat()
        })

        update_session_activity(user_id)

        # Broadcast completion notification
        broadcast_notification(user_id, f'Tool execution completed in {duration:.2f} seconds', 'success')
        broadcast_session_update(user_id, 'tool_execution_complete', {
            'output_path': output_path,
            'duration_seconds': duration,
            'lines_processed': total_output
        })

        return jsonify({
            'file_path': output_path,
            'processing_time': f"{duration:.2f} seconds",
            'lines_processed': total_output
        })

    except Exception as e:
        # Log execution error
        log_user_activity(user_id, 'tool_execution_error', {
            'error': str(e),
            'tool_path': tool_path,
            'mode': mode
        })

        return jsonify({
            'error': 'Tool execution failed',
            'message': str(e)
        }), 500

# WebSocket event handlers
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    if is_authenticated():
        user_id = session['user_id']
        join_room(f"user_{user_id}")

        # Emit welcome message
        emit('notification', {
            'type': 'info',
            'message': f'Connected to real-time updates',
            'timestamp': datetime.datetime.now().isoformat()
        })

        # Send current session status
        emit('session_status', {
            'active_sessions': len(ACTIVE_SESSIONS),
            'user_activities': len(USER_ACTIVITIES.get(user_id, []))
        })

        log_user_activity(user_id, 'websocket_connect', {
            'timestamp': datetime.datetime.now().isoformat()
        })
    else:
        emit('auth_required', {'message': 'Authentication required'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    if is_authenticated():
        user_id = session['user_id']
        leave_room(f"user_{user_id}")

        log_user_activity(user_id, 'websocket_disconnect', {
            'timestamp': datetime.datetime.now().isoformat()
        })

@socketio.on('request_session_update')
def handle_session_update_request():
    """Handle request for session updates"""
    if is_authenticated():
        user_id = session['user_id']

        emit('session_update', {
            'active_sessions': ACTIVE_SESSIONS,
            'user_activities': USER_ACTIVITIES.get(user_id, [])[-10:],  # Last 10 activities
            'timestamp': datetime.datetime.now().isoformat()
        })

def broadcast_session_update(user_id, update_type, data):
    """Broadcast session updates to specific user"""
    socketio.emit('session_update', {
        'type': update_type,
        'data': data,
        'timestamp': datetime.datetime.now().isoformat()
    }, room=f"user_{user_id}")

def broadcast_notification(user_id, message, notification_type='info'):
    """Broadcast notification to specific user"""
    socketio.emit('notification', {
        'type': notification_type,
        'message': message,
        'timestamp': datetime.datetime.now().isoformat()
    }, room=f"user_{user_id}")

# Background task for periodic updates
def background_session_monitor():
    """Monitor sessions and send periodic updates"""
    while True:
        try:
            # Clean up inactive sessions (older than 1 hour)
            current_time = datetime.datetime.now()
            inactive_users = []

            for user_id, session_data in ACTIVE_SESSIONS.items():
                last_activity = datetime.datetime.fromisoformat(session_data['last_activity'])
                if (current_time - last_activity).total_seconds() > 3600:  # 1 hour
                    inactive_users.append(user_id)

            for user_id in inactive_users:
                del ACTIVE_SESSIONS[user_id]
                broadcast_notification(user_id, 'Session expired due to inactivity', 'warning')

            # Broadcast active session count to all users
            socketio.emit('global_session_count', {
                'active_sessions': len(ACTIVE_SESSIONS),
                'timestamp': current_time.isoformat()
            })

            sleep(30)  # Check every 30 seconds

        except Exception as e:
            print(f"Background monitor error: {e}")
            sleep(60)

# Start background monitor in a separate thread
import threading
monitor_thread = threading.Thread(target=background_session_monitor, daemon=True)
monitor_thread.start()

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)