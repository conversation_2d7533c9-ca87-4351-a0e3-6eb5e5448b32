/* Space Theme Variables */
:root {
    --space-primary: #0f33ff;
    --space-secondary: #0a28cc;
    --space-accent: #00ff88;
    --space-bg: #000000;
    --space-surface: rgba(32, 32, 32, 0.9);
    --space-text: #f0f8ff;
    --space-text-secondary: #b0c4de;
    --space-border: rgba(15, 51, 255, 0.3);
    --space-error: #ff3366;
    --space-success: #00ff88;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.5rem;
    --font-size-2xl: 2rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    background: linear-gradient(135deg, #000000 0%, #0a0a2e 50%, #16213e 100%);
    color: var(--space-text);
    min-height: 100vh;
    overflow: hidden;
    position: relative;
}

/* Animated Background */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(rgba(15, 51, 255, 0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(15, 51, 255, 0.08) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: moveStars 20s linear infinite;
    z-index: 1;
}

.twinkling {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%2300ff88" opacity="0.8"><animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="0.5" fill="%230f33ff" opacity="0.6"><animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="70" r="0.8" fill="%2300ff88" opacity="0.7"><animate attributeName="opacity" values="0.7;0.3;0.7" dur="2.5s" repeatCount="indefinite"/></circle></svg>') repeat;
    background-size: 200px 200px;
    animation: twinkle 10s linear infinite;
    z-index: 2;
}

@keyframes moveStars {
    0% { transform: translateY(0); }
    100% { transform: translateY(-20px); }
}

@keyframes twinkle {
    0% { background-position: 0 0; }
    100% { background-position: 200px 200px; }
}

/* Glass Morphism Effect */
.glass-panel {
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: 2px solid var(--space-border);
    border-radius: 15px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Login Container */
.login-container {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: var(--spacing-lg);
}

.login-card {
    width: 100%;
    max-width: 400px;
    padding: var(--spacing-2xl);
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo Section */
.logo-section {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.logo {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--space-primary);
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 0 20px rgba(15, 51, 255, 0.5);
    letter-spacing: 2px;
}

.subtitle {
    font-size: var(--font-size-sm);
    color: var(--space-text-secondary);
    font-weight: 300;
    letter-spacing: 1px;
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.input-group label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--space-text);
    letter-spacing: 0.5px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--space-border);
    border-radius: 8px;
    color: var(--space-text);
    font-size: var(--font-size-md);
    font-family: 'Montserrat', sans-serif;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--space-accent);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.input-wrapper input::placeholder {
    color: var(--space-text-secondary);
    opacity: 0.7;
}

.input-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--space-text-secondary);
    font-size: var(--font-size-md);
    z-index: 1;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    background: none;
    border: none;
    color: var(--space-text-secondary);
    cursor: pointer;
    font-size: var(--font-size-md);
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--space-accent);
}

.input-feedback {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.input-feedback.valid {
    background: var(--space-success);
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.input-feedback.invalid {
    background: var(--space-error);
    box-shadow: 0 0 10px rgba(255, 51, 102, 0.5);
}

/* Checkbox Styles */
.form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--space-text-secondary);
    user-select: none;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--space-border);
    border-radius: 4px;
    margin-right: var(--spacing-sm);
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: var(--space-accent);
    border-color: var(--space-accent);
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--space-bg);
    font-size: 12px;
    font-weight: bold;
}

/* Login Button */
.login-button {
    background: linear-gradient(135deg, var(--space-primary), var(--space-secondary));
    border: none;
    border-radius: 8px;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--space-text);
    font-size: var(--font-size-md);
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    letter-spacing: 1px;
    margin-top: var(--spacing-md);
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(15, 51, 255, 0.4);
}

.login-button:active {
    transform: translateY(0);
}

.login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.login-button:hover::before {
    left: 100%;
}

.button-icon {
    transition: transform 0.3s ease;
}

.login-button:hover .button-icon {
    transform: translateX(3px);
}

/* Login Footer */
.login-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--space-border);
}

.login-footer p {
    font-size: var(--font-size-xs);
    color: var(--space-text-secondary);
    opacity: 0.8;
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: 2px solid var(--space-border);
    border-radius: 8px;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--space-text);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-color: var(--space-success);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.2);
}

.notification.error {
    border-color: var(--space-error);
    box-shadow: 0 0 20px rgba(255, 51, 102, 0.2);
}

.notification.info {
    border-color: var(--space-primary);
    box-shadow: 0 0 20px rgba(15, 51, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: var(--spacing-md);
    }
    
    .login-card {
        padding: var(--spacing-xl);
    }
    
    .logo {
        font-size: var(--font-size-xl);
    }
    
    .notification-container {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: var(--spacing-sm);
    }
    
    .notification {
        max-width: none;
    }
}

/* Loading State */
.login-button.loading {
    pointer-events: none;
    opacity: 0.7;
}

.login-button.loading .button-text {
    opacity: 0;
}

.login-button.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid var(--space-text);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Glow Animations */
@keyframes blueGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(15, 51, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(15, 51, 255, 0.6); }
}

.glass-panel {
    animation: blueGlow 3s ease-in-out infinite;
}
