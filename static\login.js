// Login functionality with real-time validation and space theme
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const loginForm = document.getElementById('loginForm');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const loginButton = document.getElementById('loginButton');
    const rememberMeCheckbox = document.getElementById('rememberMe');
    const notificationContainer = document.getElementById('notificationContainer');

    // Email validation pattern for @company.com domain
    const emailPattern = /^[a-zA-Z0-9._%+-]+@company\.com$/;

    // Real-time email validation
    emailInput.addEventListener('input', function() {
        validateEmailRealTime(this);
    });

    // Real-time password validation
    passwordInput.addEventListener('input', function() {
        validatePasswordRealTime(this);
    });

    // Password toggle functionality
    passwordToggle.addEventListener('click', function() {
        togglePasswordVisibility();
    });

    // Form submission
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && document.activeElement !== loginButton) {
            e.preventDefault();
            handleLogin();
        }
        if (e.key === 'Escape') {
            clearForm();
        }
    });

    // Load remembered email if exists
    loadRememberedCredentials();

    /**
     * Real-time email validation with visual feedback
     */
    function validateEmailRealTime(input) {
        const email = input.value.trim();
        const inputWrapper = input.closest('.input-wrapper');
        const feedback = inputWrapper.querySelector('.input-feedback');
        
        if (email === '') {
            // Reset state when empty
            input.style.borderColor = 'var(--space-border)';
            feedback.className = 'input-feedback';
            return false;
        }
        
        const isValid = emailPattern.test(email);
        
        if (isValid) {
            input.style.borderColor = 'var(--space-success)';
            input.style.boxShadow = '0 0 10px rgba(0, 255, 136, 0.3)';
            feedback.className = 'input-feedback valid';
        } else {
            input.style.borderColor = 'var(--space-error)';
            input.style.boxShadow = '0 0 10px rgba(255, 51, 102, 0.3)';
            feedback.className = 'input-feedback invalid';
        }
        
        return isValid;
    }

    /**
     * Real-time password validation
     */
    function validatePasswordRealTime(input) {
        const password = input.value;
        const inputWrapper = input.closest('.input-wrapper');
        
        if (password === '') {
            input.style.borderColor = 'var(--space-border)';
            return false;
        }
        
        const isValid = password.length >= 6; // Minimum password length
        
        if (isValid) {
            input.style.borderColor = 'var(--space-success)';
            input.style.boxShadow = '0 0 10px rgba(0, 255, 136, 0.3)';
        } else {
            input.style.borderColor = 'var(--space-error)';
            input.style.boxShadow = '0 0 10px rgba(255, 51, 102, 0.3)';
        }
        
        return isValid;
    }

    /**
     * Toggle password visibility
     */
    function togglePasswordVisibility() {
        const icon = passwordToggle.querySelector('i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    /**
     * Handle login form submission
     */
    async function handleLogin() {
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        const rememberMe = rememberMeCheckbox.checked;

        // Validate inputs
        const isEmailValid = validateEmailRealTime(emailInput);
        const isPasswordValid = validatePasswordRealTime(passwordInput);

        if (!isEmailValid) {
            showNotification('Please enter a valid @company.com email address', 'error');
            emailInput.focus();
            return;
        }

        if (!isPasswordValid) {
            showNotification('Password must be at least 6 characters long', 'error');
            passwordInput.focus();
            return;
        }

        // Show loading state
        setLoadingState(true);

        try {
            // Send login request
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    password: password,
                    remember_me: rememberMe
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // Save credentials if remember me is checked
                if (rememberMe) {
                    localStorage.setItem('rememberedEmail', email);
                } else {
                    localStorage.removeItem('rememberedEmail');
                }

                showNotification('Login successful! Launching mission...', 'success');
                
                // Redirect to main application after short delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 1500);
            } else {
                showNotification(data.message || 'Login failed. Please check your credentials.', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            showNotification('Connection error. Please try again.', 'error');
        } finally {
            setLoadingState(false);
        }
    }

    /**
     * Show notification with animation
     */
    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const icon = getNotificationIcon(type);
        notification.innerHTML = `<i class="fas fa-${icon}"></i>${message}`;
        
        notificationContainer.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    /**
     * Get appropriate icon for notification type
     */
    function getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-circle';
            case 'info': return 'info-circle';
            default: return 'bell';
        }
    }

    /**
     * Set loading state for login button
     */
    function setLoadingState(loading) {
        if (loading) {
            loginButton.classList.add('loading');
            loginButton.disabled = true;
        } else {
            loginButton.classList.remove('loading');
            loginButton.disabled = false;
        }
    }

    /**
     * Clear form inputs
     */
    function clearForm() {
        emailInput.value = '';
        passwordInput.value = '';
        rememberMeCheckbox.checked = false;
        
        // Reset input styles
        [emailInput, passwordInput].forEach(input => {
            input.style.borderColor = 'var(--space-border)';
            input.style.boxShadow = 'none';
            const feedback = input.closest('.input-wrapper').querySelector('.input-feedback');
            if (feedback) {
                feedback.className = 'input-feedback';
            }
        });
    }

    /**
     * Load remembered credentials
     */
    function loadRememberedCredentials() {
        const rememberedEmail = localStorage.getItem('rememberedEmail');
        if (rememberedEmail) {
            emailInput.value = rememberedEmail;
            rememberMeCheckbox.checked = true;
            validateEmailRealTime(emailInput);
            passwordInput.focus();
        } else {
            emailInput.focus();
        }
    }

    /**
     * Add smooth focus transitions
     */
    [emailInput, passwordInput].forEach(input => {
        input.addEventListener('focus', function() {
            this.closest('.input-wrapper').style.transform = 'scale(1.02)';
            this.closest('.input-wrapper').style.transition = 'transform 0.2s ease';
        });

        input.addEventListener('blur', function() {
            this.closest('.input-wrapper').style.transform = 'scale(1)';
        });
    });

    /**
     * Add particle effect on successful login (optional enhancement)
     */
    function createParticleEffect() {
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: var(--space-accent);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
                left: 50%;
                top: 50%;
                animation: particle-explosion 1s ease-out forwards;
                animation-delay: ${i * 0.05}s;
            `;
            document.body.appendChild(particle);
            
            setTimeout(() => particle.remove(), 1000);
        }
    }

    // Add particle animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes particle-explosion {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(
                    calc(-50% + ${Math.random() * 200 - 100}px),
                    calc(-50% + ${Math.random() * 200 - 100}px)
                ) scale(0);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});
