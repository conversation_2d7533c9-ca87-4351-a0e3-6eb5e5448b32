// Main application logic for AutoSpace
import toolsData from './data.js';

// Application state
let uploadedFile = null;
let selectedTool = null;
let currentUser = null;

// DOM Elements
let navTabs, toolsSection, descriptionBox, runButton, uploadButton, downloadSampleButton;
let dButtons, fileInput, modeButton, active_sessions, finished_sessions;
let userInfo, logoutBtn, notificationContainer;

// Initialize application
export async function initializeApp() {
    // Get DOM elements
    getDOMElements();
    
    // Check authentication
    await checkAuthentication();
    
    // Setup event listeners
    setupEventListeners();
    
    // Initialize UI
    initializeUI();
    
    // Start session polling
    startSessionPolling();
}

// Get all DOM elements
function getDOMElements() {
    navTabs = document.querySelectorAll('.tab');
    toolsSection = document.querySelector('.tools-section');
    descriptionBox = document.querySelector('.description-box');
    runButton = descriptionBox.querySelector('.run-button');
    uploadButton = descriptionBox.querySelector('.upload-button');
    downloadSampleButton = descriptionBox.querySelector('.download-sample-button');
    dButtons = document.querySelectorAll('.d-buttons');
    fileInput = document.getElementById('file-input');
    modeButton = document.querySelector('.mode-button');
    active_sessions = document.querySelector('.progress-box');
    finished_sessions = document.querySelector('.finished-box');
    userInfo = document.getElementById('userInfo');
    logoutBtn = document.getElementById('logoutBtn');
    notificationContainer = document.getElementById('notificationContainer');
}

// Check authentication status
async function checkAuthentication() {
    try {
        const response = await fetch('/api/session');
        const sessionData = await response.json();
        
        if (!sessionData.authenticated) {
            window.location.href = '/login';
            return;
        }
        
        currentUser = sessionData.user;
        userInfo.textContent = `Welcome, ${currentUser.name}`;
        logoutBtn.style.display = 'inline-block';
        
        showNotification(`Welcome back, ${currentUser.name}! 🚀`, 'success');
        
    } catch (error) {
        console.error('Failed to check authentication:', error);
        showNotification('Failed to load user session', 'error');
        setTimeout(() => window.location.href = '/login', 2000);
    }
}

// Setup all event listeners
function setupEventListeners() {
    // Logout functionality
    logoutBtn.addEventListener('click', handleLogout);
    
    // File input handler
    fileInput.addEventListener('change', handleFileInput);
    
    // Tab navigation
    navTabs.forEach(tab => {
        tab.addEventListener('click', () => handleTabClick(tab));
    });
    
    // Run button
    runButton.addEventListener('click', handleRunTool);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// Handle logout
async function handleLogout() {
    try {
        await fetch('/api/logout', { method: 'POST' });
        showNotification('Logged out successfully', 'info');
        setTimeout(() => {
            window.location.href = '/login';
        }, 1000);
    } catch (error) {
        console.error('Logout failed:', error);
        showNotification('Logout failed', 'error');
    }
}

// Handle file input
function handleFileInput() {
    const file = fileInput.files[0];
    if (file) {
        uploadedFile = file;
        showNotification(`File "${file.name}" selected`, 'info');
        
        // Add visual feedback
        uploadButton.textContent = `✓ ${file.name}`;
        uploadButton.style.borderColor = 'var(--space-success)';
    }
}

// Handle tab clicks
function handleTabClick(tab) {
    // Remove active class from all tabs
    navTabs.forEach(t => t.classList.remove('active'));
    tab.classList.add('active');
    
    // Clear tools section and description
    toolsSection.innerHTML = '';
    clearDescription();
    hideButtons();
    
    // Load tools for selected category
    loadToolsForCategory(tab.id);
}

// Clear description box
function clearDescription() {
    descriptionBox.childNodes.forEach(child => {
        if (child.childNodes) {
            child.childNodes.forEach(subChild => {
                subChild.textContent = '';
            });
        } else {
            child.textContent = '';
        }
    });
}

// Hide all buttons
function hideButtons() {
    dButtons.forEach(button => {
        button.style.display = 'none';
    });
}

// Load tools for category
function loadToolsForCategory(categoryId) {
    const categoryTools = toolsData.filter(tool => tool.id === categoryId);
    
    categoryTools.forEach(tool => {
        const toolCard = createToolCard(tool);
        toolsSection.appendChild(toolCard);
    });
}

// Create tool card element
function createToolCard(tool) {
    const toolCard = document.createElement('div');
    toolCard.className = 'tool-card box';
    toolCard.innerHTML = `
        <span>
            <h3>${tool.name}</h3>
        </span>
    `;
    
    toolCard.style.cursor = 'pointer';
    toolCard.style.textTransform = 'uppercase';
    
    toolCard.addEventListener('click', () => selectTool(tool));
    
    return toolCard;
}

// Select a tool
function selectTool(tool) {
    descriptionBox.querySelector('.tool-description').innerHTML = tool.description.replace(/\n/g, '<br>');
    descriptionBox.querySelector('.tool-name').innerHTML = tool.name;
    
    // Update button text
    downloadSampleButton.textContent = 'Download Sample';
    runButton.textContent = 'Run';
    uploadButton.textContent = 'Upload';
    
    selectedTool = tool.name.replace(/\s/g, '_').toLowerCase();
    
    // Show buttons
    dButtons.forEach(button => {
        button.style.display = 'block';
        button.id = selectedTool;
    });
    
    showNotification(`Tool "${tool.name}" selected`, 'info');
}

// Handle run tool
async function handleRunTool() {
    if (!uploadedFile) {
        showNotification('Please upload a file first', 'warning');
        return;
    }
    
    if (!selectedTool) {
        showNotification('Please select a tool first', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', uploadedFile);
    formData.append('tool', selectedTool);
    formData.append('mode', modeButton.checked ? 'fast' : 'normal');
    
    const runMode = modeButton.checked ? 'fast' : 'normal';
    let sessionId = null;
    
    try {
        // Create session
        sessionId = updateSessions('Uploading', 'write', '', runButton.id, '', runMode);
        
        // Upload file
        const uploadResponse = await fetch('/upload', {
            method: 'POST',
            body: formData,
        });
        
        const uploadData = await uploadResponse.json();
        
        if (uploadData.message === 'success') {
            updateSessions('Running', 'update', sessionId, runButton.id, '', runMode);
            
            // Run tool
            const runResponse = await fetch('/runtool', {
                method: 'POST'
            });
            
            const runData = await runResponse.json();
            
            if (runData.file_path) {
                updateSessions('Finished', 'finish', sessionId, runButton.id, runData.file_path, runMode);
                showNotification('Tool execution completed successfully!', 'success');
            }
        }
    } catch (error) {
        console.error('Error running tool:', error);
        showNotification('Tool execution failed', 'error');
        if (sessionId) {
            updateSessions('Failed', 'finish', sessionId, runButton.id, '', runMode);
        }
    }
    
    // Reset file input
    uploadedFile = null;
    fileInput.value = '';
    uploadButton.textContent = 'Upload';
    uploadButton.style.borderColor = 'var(--space-border)';
}

// Handle keyboard shortcuts
function handleKeyboardShortcuts(e) {
    if (e.key === 'Enter' && e.ctrlKey) {
        e.preventDefault();
        handleRunTool();
    }
    
    if (e.key === 'Escape') {
        clearSelection();
    }
}

// Clear current selection
function clearSelection() {
    uploadedFile = null;
    selectedTool = null;
    fileInput.value = '';
    clearDescription();
    hideButtons();
    
    navTabs.forEach(tab => tab.classList.remove('active'));
    toolsSection.innerHTML = '';
    
    showNotification('Selection cleared', 'info');
}

// Initialize UI
function initializeUI() {
    showPerformance();
}

// Show system performance info
function showPerformance() {
    const progressBox = document.querySelector('.visualization-box');
    const systemInfoList = document.createElement('ul');
    
    systemInfoList.innerHTML = `
        <li>Total Device Memory: ${navigator.deviceMemory || 'Unknown'} GB</li>
        <li>Hardware Concurrency: ${navigator.hardwareConcurrency || 'Unknown'}</li>
        <li>Platform: ${navigator.platform}</li>
        <li>User Agent: ${navigator.userAgent.split(' ')[0]}</li>
    `;
    
    progressBox.appendChild(systemInfoList);
}

// Session polling for real-time updates
function startSessionPolling() {
    setInterval(async () => {
        try {
            const response = await fetch('/api/session');
            const sessionData = await response.json();
            
            if (!sessionData.authenticated) {
                window.location.href = '/login';
            }
        } catch (error) {
            console.error('Session check failed:', error);
        }
    }, 30000); // Check every 30 seconds
}

// Session management functions
function updateSessions(status, mode, sessionId, buttonId, filePath, runMode) {
    const time = new Date();
    const strTime = `${time.getHours()}:${time.getMinutes()}`;
    const idTime = `${time.getHours()}${time.getMinutes()}${time.getSeconds()}`;
    
    if (mode === 'write') {
        const sessionCard = document.createElement('div');
        sessionCard.className = 'session-card';
        sessionCard.id = idTime;
        sessionCard.innerHTML = `              
            <span class="tool name">${buttonId}</span>
            <div class='session-info'>
                <span class="session-indicator" style="color: yellow;">◉</span>
                <span class="session-time">${strTime}</span>
                <span class="session-status">${status}</span>
                <span class="session-mode">${runMode}</span>                
            </div>
        `;
        active_sessions.appendChild(sessionCard);
        return idTime;
    } else if (mode === 'update') {
        const sessionCard = document.getElementById(sessionId);
        if (sessionCard) {
            sessionCard.innerHTML = `              
                <span class="tool name">${buttonId}</span>
                <div class='session-info'>
                    <span class="session-indicator" style="color: yellowgreen;">◉</span>
                    <span class="session-time">${strTime}</span>
                    <span class="session-status">${status}</span>
                    <span class="session-mode">${runMode}</span>
                </div>
            `;
        }
    } else if (mode === 'finish') {
        const sessionCard = document.getElementById(sessionId);
        if (sessionCard) {
            sessionCard.innerHTML = `              
                <span class="tool name">${buttonId}</span>
                <div class='session-info'>
                    <span class="session-indicator" style="color: black;">◉</span>
                    <span class="session-time">${strTime}</span>
                    <span class="session-status">${status}</span>
                    <span class="session-mode">${runMode}</span>                
                </div>
                <span class="click-copy">click to copy path</span>
            `;
            
            active_sessions.removeChild(sessionCard);
            finished_sessions.appendChild(sessionCard);
            
            sessionCard.addEventListener('click', () => {
                sessionCard.querySelector('.click-copy').textContent = 'copied!';
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(filePath).catch(err => {
                        console.error("Clipboard write failed:", err);
                    });
                } else {
                    showNotification("Clipboard not supported in this browser", 'warning');
                }
            });
        }
    }
}

// Notification system
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icon = getNotificationIcon(type);
    notification.innerHTML = `<i class="fas fa-${icon}"></i>${message}`;
    
    notificationContainer.appendChild(notification);
    
    setTimeout(() => notification.classList.add('show'), 100);
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'info': return 'info-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'bell';
    }
}
