/* Space Theme Variables */
:root {
    /* Space Colors */
    --space-primary: #0f33ff;
    --space-secondary: #0a28cc;
    --space-accent: #00ff88;
    --space-bg: #000000;
    --space-surface: rgba(32, 32, 32, 0.9);
    --space-text: #f0f8ff;
    --space-text-secondary: #b0c4de;
    --space-border: rgba(15, 51, 255, 0.3);
    --space-error: #ff3366;
    --space-success: #00ff88;
    --space-warning: #ffaa00;

    /* Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.5rem;
    --font-size-2xl: 2rem;

    /* Layout */
    --border-standard: 2px solid var(--space-border);
    --box-padding: var(--spacing-lg);
    --letter-spacing-standard: 0.5px;
    --letter-spacing-wide: 3px;
    --section-gap: var(--spacing-sm);
    --card-gap: var(--spacing-md);
    --box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    --border-radius-sm: 8px;
    --border-radius-md: 15px;
}

/* Base styles */
* {
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    padding: 0;
}

body {
    background:
        linear-gradient(rgba(15, 51, 255, 0.08) 1px, transparent 1px),
        linear-gradient(90deg, rgba(15, 51, 255, 0.08) 1px, transparent 1px),
        linear-gradient(135deg, #000000 0%, #0a0a2e 50%, #16213e 100%);
    background-size: 20px 20px, 20px 20px, 100% 100%;
    background-attachment: fixed;
    color: var(--space-text);
    line-height: 1.6;
    margin: 0;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Animated stars background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="0.5" fill="%2300ff88" opacity="0.8"><animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="0.3" fill="%230f33ff" opacity="0.6"><animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="70" r="0.4" fill="%2300ff88" opacity="0.7"><animate attributeName="opacity" values="0.7;0.3;0.7" dur="2.5s" repeatCount="indefinite"/></circle><circle cx="60" cy="10" r="0.2" fill="%230f33ff" opacity="0.5"><animate attributeName="opacity" values="0.5;0.1;0.5" dur="4s" repeatCount="indefinite"/></circle></svg>') repeat;
    background-size: 200px 200px;
    animation: twinkle 15s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes twinkle {
    0% { background-position: 0 0; }
    100% { background-position: 200px 200px; }
}

.container {
    display: grid;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    width: 100%;
    position: relative;
    z-index: 2;
}

/* Glass Morphism Effect */
.glass-panel {
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: var(--border-standard);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
}

/* Common styles */
.section-container {
    display: flex;
    flex-direction: column;
    gap: var(--section-gap);
    background: transparent;
}

.box {
    padding: var(--box-padding);
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: var(--border-standard);
    margin: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.box:hover {
    border-color: var(--space-accent);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 255, 136, 0.2);
    transform: translateY(-2px);
}



.box-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    letter-spacing: var(--letter-spacing-wide);
    color: var(--space-text);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--space-accent);
    display: inline-block;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.secondary-text {
    font-size: var(--font-size-sm);
    color: var(--space-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

/* Top Bar */
.top-bar {
    height: 60px;
    margin-bottom: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-xl);
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border-bottom: var(--border-standard);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 10;
}

.logo {
    font-weight: 700;
    font-size: var(--font-size-xl);
    letter-spacing: var(--letter-spacing-wide);
    color: var(--space-primary);
    text-shadow: 0 0 15px rgba(15, 51, 255, 0.5);
    animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% { text-shadow: 0 0 15px rgba(15, 51, 255, 0.5); }
    50% { text-shadow: 0 0 25px rgba(15, 51, 255, 0.8); }
}

.top-menu {
    display: flex;
    gap: var(--spacing-2xl);
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: var(--spacing-2xl);
}

nav ul li {
    margin-right: 0;
}

nav ul li a {
    text-decoration: none;
    color: var(--space-text);
    font-size: var(--font-size-md);
    font-weight: 500;
    letter-spacing: var(--letter-spacing-standard);
    transition: all 0.3s ease;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    position: relative;
    overflow: hidden;
}

nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.2), transparent);
    transition: left 0.5s ease;
}

nav ul li a:hover::before {
    left: 100%;
}

nav ul li a:hover {
    color: var(--space-accent);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    transform: translateY(-1px);
}

.account {
    font-size: var(--font-size-md);
    color: var(--space-text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--space-border);
    border-radius: var(--border-radius-sm);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logout-button {
    background: linear-gradient(135deg, var(--space-error), #cc2244);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--space-text);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.logout-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(255, 51, 102, 0.3);
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: 2px solid var(--space-border);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--space-text);
    font-size: var(--font-size-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
    box-shadow: var(--box-shadow);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-color: var(--space-success);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.2);
}

.notification.error {
    border-color: var(--space-error);
    box-shadow: 0 0 20px rgba(255, 51, 102, 0.2);
}

.notification.info {
    border-color: var(--space-primary);
    box-shadow: 0 0 20px rgba(15, 51, 255, 0.2);
}

.notification.warning {
    border-color: var(--space-warning);
    box-shadow: 0 0 20px rgba(255, 170, 0, 0.2);
}

/* Content Area */
.content {
    display: grid;
    grid-template-columns: 220px 420px 1.5fr 1fr;
    grid-template-rows: 1fr;
    min-height: calc(100vh - 140px);
    background: transparent;
    gap: var(--spacing-sm);
    padding: 0 var(--spacing-md);
}

/* Side Bar */
.side-bar {
    margin-top: var(--spacing-md);
    display: flex;
    flex-direction: column;
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: var(--border-standard);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--box-shadow);
}

.tabs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    width: 100%;
}

.tab {
    width: 100%;
    display: flex;
    align-items: center;
    height: 60px;
    font-size: var(--font-size-sm);
    color: var(--space-text);
    letter-spacing: var(--letter-spacing-standard);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--space-border);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    position: relative;
    overflow: hidden;
}

.tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(15, 51, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.tab:hover::before {
    left: 100%;
}

.tab.active {
    background: linear-gradient(135deg, var(--space-primary), var(--space-secondary));
    border-color: var(--space-accent);
    box-shadow: 0 0 20px rgba(15, 51, 255, 0.4);
}

.tab span {
    width: 100%;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--space-text);
    margin-left: var(--spacing-md);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

.tab:hover {
    border-color: var(--space-accent);
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.2);
}

.tab:hover span {
    color: var(--space-accent);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* Tool Cards Section */
.tools-section {
    background: var(--space-surface);
    backdrop-filter: blur(10px);
    border: var(--border-standard);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-md);
    box-shadow: var(--box-shadow);
}

.tool-card {
    padding: var(--spacing-lg);
    height: 120px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    gap: var(--card-gap);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-md);
    border: 2px solid var(--space-border);
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    transition: left 0.5s ease;
}

.tool-card:hover::before {
    left: 100%;
}

.tool-card:hover {
    border-color: var(--space-accent);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 136, 0.2);
}

.tool-card span {
    display: flex;
    align-items: center;
    gap: var(--card-gap);
}

.tool-card h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--space-text);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-standard);
    text-shadow: 0 0 10px rgba(240, 248, 255, 0.3);
}

.tool-card p {
    font-size: var(--font-size-sm);
    color: var(--space-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.tool-card span {
    font-size: var(--font-size-sm);
    color: var(--space-text-secondary);
}
.section h3{
    padding: 10px;
}

/* Middle Section */
.middle-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.section{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

.description-box .tool-description{
    margin:0px;

}

.description-box{
    justify-content: space-between;
}

.tool-name {
    font-weight: bold;
    margin-bottom: var(--spacing-md);
}

.tool-description {
    margin-bottom: var(--spacing-lg);
    white-space: pre-line;
}

.button-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}
.progress-section{
    flex-grow: 1;
}
.smaller-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.progress-box, .finished-box {
    flex-grow:  1;
    border-radius: var(--border-radius-sm);
}
.session-card{
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    border-left: 4px solid var(--color-border);
    transition: all 0.2s ease;
}
.session-card:hover {
    cursor: pointer;
    border-color: var(--color-text-secondary);
}
.session-card:active{
    opacity: 0.5;
}

.output-file{
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.session-info, .click-copy{
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
}

/* Right Section */
.right-section {
    background-color: var(--color-bg);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.visualization-box {
    flex-grow: 1;
}

.visualization-box ul {
    list-style-position: inside;
    padding-left: var(--spacing-md);
}

.visualization-box ul li {
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.visualization-box ul li::marker {
    color: red;
}

.users-box {
    height: 180px;
    border-radius: var(--border-radius-sm);
}

.user-count {
    color: var(--color-text-secondary);
    text-align: center;
    padding: var(--spacing-xl) 0;
    font-style: italic;
}

/* Footer */
footer {
    padding: var(--spacing-lg);
    text-align: center;
    background-color: var(--color-bg);
    border-top: var(--border-standard);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.upload-button, .run-button, .download-sample-button {
    display: none;
    margin-top: var(--spacing-md);
    margin-right: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-md);
    font-weight: 600;
    font-family: 'Montserrat', sans-serif;
    cursor: pointer;
    color: var(--space-text);
    background: linear-gradient(135deg, var(--space-primary), var(--space-secondary));
    border: 2px solid var(--space-border);
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    outline: none;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-standard);
}

.upload-button::before, .run-button::before, .download-sample-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.upload-button:hover::before, .run-button:hover::before, .download-sample-button:hover::before {
    left: 100%;
}

.upload-button:hover, .run-button:hover, .download-sample-button:hover {
    border-color: var(--space-accent);
    box-shadow: 0 10px 25px rgba(15, 51, 255, 0.4);
    transform: translateY(-2px);
}

.upload-button:active, .run-button:active, .download-sample-button:active {
    transform: translateY(0);
}

/* Special styling for run button */
.run-button {
    background: linear-gradient(135deg, var(--space-accent), #00cc77);
}

.run-button:hover {
    box-shadow: 0 10px 25px rgba(0, 255, 136, 0.4);
}