/* Variables */
:root {
    /* Colors */
    --color-bg: #f5f5f5;
    --color-text: #2a2a2a;
    --color-text-secondary: #666;
    --color-border: #e0e0e0;
    --color-white: #fff;

    /* Spacing */
    --spacing-xs: 5px;
    --spacing-sm: 8px;
    --spacing-md: 10px;
    --spacing-lg: 15px;
    --spacing-xl: 20px;
    --spacing-xxl: 30px;

    /* Typography */
    --font-size-xs: 10px;
    --font-size-sm: 12px;
    --font-size-md: 14px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 42px;

    /* Layout */
    --border-standard: 1px solid var(--color-border);
    --box-padding: var(--spacing-lg);
    --letter-spacing-standard: 0.5px;
    --letter-spacing-wide: 1px;
    --section-gap: 0;
    --card-gap: var(--spacing-md);
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    --border-radius-sm: 2px;
}

/* Base styles */
* {
    box-sizing: border-box;
    font-family: 'Space Mono', monospace;
    margin: 0;
    padding: 0;
}

body {
    background-color: var(--color-bg);
    color: var(--color-text);
    line-height: 1.6;
    margin: 0;
}

.container {
    display: grid;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
    width: 100%;
}

/* Common styles */
.section-container {
    display: flex;
    flex-direction: column;
    gap: var(--section-gap);
    background-color: var(--color-bg);
}

.box {
    padding: var(--box-padding);
    background-color: var(--color-white);
    border-bottom: var(--border-standard);
    border-left: var(--border-standard);
    margin: 10px;
    border-radius: var(--border-radius-sm);
    transition: box-shadow 0.2s ease;
}



.box-title {
    font-size: var(--font-size-md);
    font-weight: bold;
    letter-spacing: var(--letter-spacing-standard);
    color: var(--color-text);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    padding-bottom: var(--spacing-xs);
    border-bottom: 1px solid var(--color-border);
    display: inline-block;
}

.secondary-text {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

/* Top Bar */
.top-bar {
    height: 30px ;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 10px;
    background-color: var(--color-bg);
    border-bottom: var(--border-standard);
}

.logo {
    font-weight: bold;
    font-size: var(--font-size-lg);
    letter-spacing: var(--letter-spacing-wide);
}

.top-menu {
    display: flex;
    gap:50px
}

nav ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 90px;
}

nav ul li {
    margin-right: var(--spacing-xxl);
}

nav ul li a {
    text-decoration: none;
    color: var(--color-text);
    font-size: var(--font-size-md);
    letter-spacing: var(--letter-spacing-standard);
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--color-text-secondary);
}

.account {
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
}

/* Content Area */
.content {
    display: grid;
    grid-template-columns: 200px 400px 1.5fr 1fr;
    grid-template-rows: 1fr;
    min-height: calc(100vh - 120px);
    background-color: var(--color-bg);
}

/* Side Bar */
.side-bar {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    border-right: var(--border-standard);
}

.tabs {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 150px;
}

.tab {
    width: 198px;
    display: flex;
    align-items: center;
    height: 50px;
    font-size: var(--font-size-sm);
    color: var(--color-text);
    letter-spacing: var(--letter-spacing-standard);
    cursor: pointer;
    border-radius: var(--border-radius-sm);
    border-bottom: 2px solid var(--color-text);
    border-top: 2px solid var(--color-text);
    transition: all 0.2s ease;
}

.tab.active {
    background-color: rgba(0, 0, 0, 0.05);
}

.tab span {
    width: 195px;
    font-size: var(--font-size-lg);
    color: var(--color-text);
    margin-left: 10px;
    transition: all 0.2s ease;
}

.tab:hover span {
    background-color: var(--color-text);
    color: var(--color-border);
}

.tab:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Tool Cards Section */
.tools-section {
    border-right: var(--border-standard);
}

.tool-card {
    padding: 10px;
    height: 100px;
    background-color: var(--color-white);
    display: flex;
    flex-direction: column;
    gap: var(--card-gap);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
}

.tool-card:hover {
    border-color: var(--color-border);
}

.tool-card span {
    display: flex;
    align-items: center;
    gap: var(--card-gap);
}

.tool-card h3 {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: bold;
    color: var(--color-text);
}

.tool-card p {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.tool-card span {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}
.section h3{
    padding: 10px;
}

/* Middle Section */
.middle-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.section{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

.description-box .tool-description{
    margin:0px;

}

.description-box{
    justify-content: space-between;
}

.tool-name {
    font-weight: bold;
    margin-bottom: var(--spacing-md);
}

.tool-description {
    margin-bottom: var(--spacing-lg);
    white-space: pre-line;
}

.button-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}
.progress-section{
    flex-grow: 1;
}
.smaller-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.progress-box, .finished-box {
    flex-grow:  1;
    border-radius: var(--border-radius-sm);
}
.session-card{
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    border-left: 4px solid var(--color-border);
    transition: all 0.2s ease;
}
.session-card:hover {
    cursor: pointer;
    border-color: var(--color-text-secondary);
}
.session-card:active{
    opacity: 0.5;
}

.output-file{
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.session-info, .click-copy{
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
}

/* Right Section */
.right-section {
    background-color: var(--color-bg);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: var(--border-standard);
}

.visualization-box {
    flex-grow: 1;
}

.visualization-box ul {
    list-style-position: inside;
    padding-left: var(--spacing-md);
}

.visualization-box ul li {
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.visualization-box ul li::marker {
    color: red;
}

.users-box {
    height: 180px;
    border-radius: var(--border-radius-sm);
}

.user-count {
    color: var(--color-text-secondary);
    text-align: center;
    padding: var(--spacing-xl) 0;
    font-style: italic;
}

/* Footer */
footer {
    padding: var(--spacing-lg);
    text-align: center;
    background-color: var(--color-bg);
    border-top: var(--border-standard);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    letter-spacing: var(--letter-spacing-standard);
}

.upload-button, .run-button, .download-sample-button {
    display: none;
    margin-top: 10px;
    margin-right: 8px;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    color: var(--color-text-secondary);
    border: 1px solid var(--color-text-secondary);
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    outline: none;
}

.upload-button:hover, .run-button:hover, .download-sample-button:hover {
    background-color: var(--color-text-secondary);
    color: var(--color-white);
}

.upload-button:active, .run-button:active, .download-sample-button:active {
    transform: translateY(1px);
}