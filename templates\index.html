<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Optimized Layout</title>
  <link rel="stylesheet" href="../static/styles.css" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Space+Mono:wght@400;700&display=swap" />
</head>
<body>
  <div class="container">
    <!-- Top Bar -->
    <header class="top-bar">
      <div class="logo">AUTOSPACE</div>
      <div class="top-menu">
        <nav>
          <ul>
            <li><a href="#">Home</a></li>
            <li><a href="#">AutoSpace</a></li>
            <li><a href="#">About</a></li>
          </ul>
        </nav>
      </div>
      <div class="account">account place holder</div>
    </header>

    <!-- Main Content Area -->
    <div class="content">
      <!-- Side Bar -->
      <aside class="side-bar">
        <div class="tabs">
          <div class="tab" id="compare"><span>COMPARE</span></div>
          <div class="tab" id="pdf"><span>PDF</span></div>
          <div class="tab" id="generic"><span>GENERIC</span></div>
          <div class="tab" id="configuration"><span>CONFIGURATION</span></div>
        </div>
      </aside>

      <!-- Tool Cards Section -->
      <section class="tools-section section-container"></section>

      <!-- Middle Section -->
      <section class="middle-section section-container">
        <div class="description-section section">
          <h3 class="box-title">AutoSpace</h3>
          <div class="description-box box">
            <p class="tool-name"></p>
            <p class="tool-description"></p>
            <div class="button-container">
              
              <label for="file-input" class="upload-button d-buttons" id="upload">Upload</label>
              <input type="file" id="file-input" style="display:none;" />
              
              <button class="run-button d-buttons" id="run">Run</button>
              <button class="download-sample-button d-buttons" id="download">Download Sample</button>
              
              <input type="checkbox" class ="mode-button d-buttons" id="run-mode" style="display:none;" />

            </div>
          </div>
        </div>
 
        <div class="progress-section section">
          <h3 class="box-title">Sessions Status</h3>
          <div class = "smaller-container">
            <div class="progress-box box">
            <h4 class="box-title">Active Sessions</h4>
            
          </div>
          <div class="finished-box box">
            <h4 class="box-title">Finished Sessions</h4>
          </div>
          </div>
          
        </div>
      </section>

      <!-- Right Section -->
      <section class="right-section section-container">
        <div class="visualization-section section">
          <h3 class="box-title">performance</h3>
          <div class="visualization-box box"></div>
        </div>
        <div class="users-section section">
          <h3 class="box-title">active users</h3>
          <div class="users-box box"></div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer>
      <div class="copyright secondary-text">
        © <EMAIL> All rights reserved.
      </div>
    </footer>
  </div>

  <script type="module">
    // Date(year, month, day, hour, minute, second, ms)
    import toolsData from '../static/data.js';
    localStorage.clear();
    // Elements
    const navTabs = document.querySelectorAll('.tab');
    const toolsSection = document.querySelector('.tools-section');
    const descriptionBox = document.querySelector('.description-box');
    const runButton = descriptionBox.querySelector('.run-button');
    const uploadButton = descriptionBox.querySelector('.upload-button');
    const downloadSampleButton = descriptionBox.querySelector('.download-sample-button');
    const dButtons = document.querySelectorAll('.d-buttons');
    const fileInput = document.getElementById('file-input');
    const modeButton = document.querySelector('.mode-button');
    const active_sessions = document.querySelector('.progress-box');
    const finished_sessions = document.querySelector('.finished-box');
    // Global variables
    let uploadedFile = null;
    let selectedTool = null;

    fileInput.addEventListener('change', () => {
      const file = fileInput.files[0];
      if (file) uploadedFile = file;
    });

    const tabClickStatus = {
      pdf: false,
      compare: false,
      generic: false,
      configuration: false,
    };

    function showPerformance() {
      const progressBox = document.querySelector('.visualization-box');
      const systemInfoList = document.createElement('ul');

      systemInfoList.innerHTML = `
        <li>Total Device Memory: ${navigator.deviceMemory}</li>
        <li>Hardware Concurrency: ${navigator.hardwareConcurrency}</li>
        <li>Platform: ${navigator.platform}</li>
      `;

      progressBox.appendChild(systemInfoList);
    }

    showPerformance();

    function updateSessions(status, mode, session_id, button_id, file_path, run_mode){
      const time = new Date();
      const str_time = `${time.getHours()}:${time.getMinutes()}`
      const id_time = `${time.getHours()}${time.getMinutes()}${time.getSeconds()}`
      if (mode == 'write'){
      const session_card = document.createElement('div');
      session_card.className = 'session-card';
      session_card.id = id_time;
      session_card.innerHTML = `              
              <span class="tool name">${button_id}</span>
              <div class = 'session-info'>
                <span class="session-indictor" style="color: yellow;">◉</span>
                <span class="session-time">${str_time}</span>
                <span class="session-status">${status}</span>
                <span class="session-mode">${run_mode}</span>                
              </div>
        `;
        active_sessions.appendChild(session_card);
        return id_time
      }
      else if(mode == 'update'){
        const session_card = document.getElementById(session_id);      
        session_card.innerHTML = `              
              <span class="tool name">${button_id}</span>
              <div class = 'session-info'>
                <span class="session-indictor" style="color: yellowgreen;">◉</span>
                <span class="session-time">${str_time}</span>
                <span class="session-status">${status}</span>
                <span class="session-mode">${run_mode}</span>
              </div>
        `;
      }
      else if(mode == 'finish'){
        const session_card = document.getElementById(session_id);
        session_card.innerHTML = `              
              <span class="tool name">${button_id}</span>
              <div class = 'session-info'>
                <span class="session-indictor" style="color: black;">◉</span>
                <span class="session-time">${str_time}</span>
                <span class="session-status">${status}</span>
                <span class="session-mode">${run_mode}</span>                
              </div>
              <span class="click-copy">click to copy path</span>
        `;
        active_sessions.removeChild(session_card);
        finished_sessions.appendChild(session_card);
        session_card.addEventListener('click', () => {
        session_card.querySelector('.click-copy').textContent = 'copied!';
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(file_path).catch(err => {
            console.error("Clipboard write failed:", err);
          });
        } else {
          alert("Clipboard not supported in this browser.");
        }
});

      }
      
    }
    
    navTabs.forEach((tab) => {
      tab.addEventListener('click', () => {
        navTabs.forEach((t) => t.classList.remove('active'));
        tab.classList.add('active');

        toolsSection.innerHTML = '';
        descriptionBox.childNodes.forEach((child) => {
            if (child.childNodes){
                child.childNodes.forEach((child) => {
                    child.textContent = '';
                });
            }
            else{
                child.textContent = '';
            }
        });
        dButtons.forEach((button) => {
            button.style.display = 'none';
        });

        toolsData.forEach((tool) => {
          if (tool.id === tab.id) {
            const toolCard = document.createElement('div');
            toolCard.className = 'tool-card box';
            toolCard.innerHTML = `
              <span>
                <h3>${tool.name}</h3>
              </span>
            `;

            toolCard.style.cursor = 'pointer';
            toolCard.style.textTransform = 'uppercase';

            toolCard.addEventListener('click', () => {
              descriptionBox.querySelector('.tool-description').innerHTML = tool.description.replace(/\n/g, '<br>');
              descriptionBox.querySelector('.tool-name').innerHTML = tool.name;
              downloadSampleButton.textContent = 'Download Sample'
              runButton.textContent = 'Run';
              uploadButton.textContent = 'Upload';

              
              selectedTool = tool.name.replace(/\s/g, '_').toLowerCase();
              dButtons.forEach((button) => {
                button.style.display = 'block';
                button.id = selectedTool;
              });

            });
            toolsSection.appendChild(toolCard);
          }
        });
      });
    });

    // Run Button Click (single event binding)
    runButton.addEventListener('click', async () => {
      if (!uploadedFile) {
        alert('Please upload a file');
        return;
      }

      if (!selectedTool) {
        alert('Please select a tool');
        return;
      }
      
      const formData = new FormData();
      let data = null;
      formData.append('file', uploadedFile);
      formData.append('tool', selectedTool);
      formData.append('mode', modeButton.checked ? 'fast' : 'normal');
      let session_id = null;
      let run_mode = modeButton.checked ? 'fast' : 'normal';
      try {
        session_id = updateSessions('Uploading', 'write', '', runButton.id, '', run_mode);
        const response = await fetch('/upload', {
          method: 'POST',
          body: formData,
        });
        data = await response.json();
        if (data.message == 'success') {
          updateSessions('Running', 'update', session_id, runButton.id, '', run_mode);
          const response = await fetch('/runtool', {
          method: 'POST'});
          data = await response.json();
          console.log(data); // Log the response data to the console.
        }
      } catch (error) {
        console.error("Error sending file:", error);
      }

      uploadedFile = null;
      fileInput.value = ''; // Clear input value
        if (session_id) {
        updateSessions('Finished', 'finish', session_id, runButton.id, data.file_path, run_mode); // Log the response data to the console.
        }
  });
  </script>
</body>
</html>
