<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>🚀 AutoSpace - Space Document Processing</title>
  <link rel="stylesheet" href="../static/styles.css" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&display=swap" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <!-- Top Bar -->
    <header class="top-bar">
      <div class="logo">🚀 AUTOSPACE</div>
      <div class="top-menu">
        <nav>
          <ul>
            <li><a href="#"><i class="fas fa-home"></i> Home</a></li>
            <li><a href="#"><i class="fas fa-rocket"></i> AutoSpace</a></li>
            <li><a href="#"><i class="fas fa-info-circle"></i> About</a></li>
          </ul>
        </nav>
      </div>
      <div class="account">
        <span id="userInfo">Loading...</span>
        <button id="logoutBtn" class="logout-button" style="display: none;">
          <i class="fas fa-sign-out-alt"></i> Logout
        </button>
      </div>
    </header>

    <!-- Main Content Area -->
    <div class="content">
      <!-- Side Bar -->
      <aside class="side-bar">
        <div class="tabs">
          <div class="tab" id="compare"><span>COMPARE</span></div>
          <div class="tab" id="pdf"><span>PDF</span></div>
          <div class="tab" id="generic"><span>GENERIC</span></div>
          <div class="tab" id="configuration"><span>CONFIGURATION</span></div>
        </div>
      </aside>

      <!-- Tool Cards Section -->
      <section class="tools-section section-container"></section>

      <!-- Middle Section -->
      <section class="middle-section section-container">
        <div class="description-section section">
          <h3 class="box-title">AutoSpace</h3>
          <div class="description-box box">
            <p class="tool-name"></p>
            <p class="tool-description"></p>
            <div class="button-container">
              
              <label for="file-input" class="upload-button d-buttons" id="upload">Upload</label>
              <input type="file" id="file-input" style="display:none;" />
              
              <button class="run-button d-buttons" id="run">Run</button>
              <button class="download-sample-button d-buttons" id="download">Download Sample</button>
              
              <input type="checkbox" class ="mode-button d-buttons" id="run-mode" style="display:none;" />

            </div>
          </div>
        </div>
 
        <div class="progress-section section">
          <h3 class="box-title">Sessions Status</h3>
          <div class = "smaller-container">
            <div class="progress-box box">
            <h4 class="box-title">Active Sessions</h4>
            
          </div>
          <div class="finished-box box">
            <h4 class="box-title">Finished Sessions</h4>
          </div>
          </div>
          
        </div>
      </section>

      <!-- Right Section -->
      <section class="right-section section-container">
        <div class="visualization-section section">
          <h3 class="box-title">performance</h3>
          <div class="visualization-box box"></div>
        </div>
        <div class="users-section section">
          <h3 class="box-title">active users</h3>
          <div class="users-box box"></div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer>
      <div class="copyright secondary-text">
        © AutoSpace 2024 - Space-Themed Document Processing Platform
      </div>
    </footer>
  </div>

  <!-- Notification Container -->
  <div id="notificationContainer" class="notification-container"></div>

  <script type="module">
    // Import main application logic
    import { initializeApp } from '../static/main.js';

    // Initialize the application when DOM is loaded
    document.addEventListener('DOMContentLoaded', initializeApp);



  </script>
</body>
</html>
